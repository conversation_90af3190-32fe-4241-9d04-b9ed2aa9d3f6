import pandas as pd
import pandas_ta as ta
import numpy as np
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

def calculate_wma(df, price_column='close', periods=[5, 10, 45, 65, 90]):
    """
    Calculate Weighted Moving Averagess for specified periods using pandas_ta.

    Args:
        df (pd.DataFrame): Input dataframe with price data
        price_column (str): Column name to use for WMA calculation (default: 'close')
        periods (list): List of periods for WMA calculation

    Returns:
        pd.DataFrame: DataFrame with added WMA columns
    """
    df_copy = df.copy()

    # Ensure the price column exists
    if price_column not in df_copy.columns:
        print(f"  ✗ Price column '{price_column}' not found in dataframe")
        # Add dummy columns to avoid errors later, though processing will be invalid
        for period in periods:
             df_copy[f'WMA{period}'] = np.nan
        return df_copy

    # Calculate WMA for each period
    for period in periods:
        try:
            # Use pandas_ta to calculate WMA
            wma_values = ta.wma(df_copy[price_column], length=period)
            df_copy[f'WMA{period}'] = wma_values
            # print(f"  ✓ Calculated WMA{period}") # Keep prints for clarity during execution
        except Exception as e:
            print(f"  ✗ Error calculating WMA{period}: {str(e)}")
            df_copy[f'WMA{period}'] = np.nan

    return df_copy


def detect_crossovers(df, fast_wma='WMA5', slow_wma='WMA10'):
    """
    Detect crossovers between two WMA series.

    Args:
        df (pd.DataFrame): DataFrame containing WMA columns
        fast_wma (str): Column name for fast WMA (default: 'WMA5')
        slow_wma (str): Column name for slow WMA (default: 'WMA10')

    Returns:
        pd.DataFrame: DataFrame with added crossover signal column
    """
    df_copy = df.copy()

    # Use standard column name for WMA5/WMA10 crossovers, custom name for others
    if fast_wma == 'WMA5' and slow_wma == 'WMA10':
        crossover_signal_name = 'crossover_signal'
    else:
        crossover_signal_name = f'crossover_signal_{fast_wma}_{slow_wma}'

    # Check if required columns exist
    if fast_wma not in df_copy.columns or slow_wma not in df_copy.columns:
        print(f"  ✗ Required WMA columns not found for crossover detection")
        df_copy[crossover_signal_name] = 'neutral'
        return df_copy

    # Initialize crossover signal column
    df_copy[crossover_signal_name] = 'neutral'

    # Calculate crossover signals
    try:
        # Get the WMA series
        fast_series = df_copy[fast_wma]
        slow_series = df_copy[slow_wma]

        # Calculate the difference and its shift
        diff = fast_series - slow_series
        diff_prev = diff.shift(1)

        # Detect crossovers
        # Upward crossover: fast WMA crosses above slow WMA
        upward_cross = (diff > 0) & (diff_prev <= 0)

        # Downward crossover: fast WMA crosses below slow WMA
        downward_cross = (diff < 0) & (diff_prev >= 0)

        # Apply signals
        df_copy.loc[upward_cross, crossover_signal_name] = 'upward'
        df_copy.loc[downward_cross, crossover_signal_name] = 'downward'

        # Count crossovers
        upward_count = upward_cross.sum()
        downward_count = downward_cross.sum()

        # print(f"  ✓ Detected {upward_count} upward and {downward_count} downward crossovers") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting crossovers: {str(e)}")

    return df_copy


def detect_supply_demand_zones(df, crossover_column='crossover_signal'):
    """
    Detect supply and demand zones based on crossover transitions.

    Supply Zones: Price ranges between 'upward' → 'downward' crossover transitions
    - Captures the candle with highest 'high' price in the period

    Demand Zones: Price ranges between 'downward' → 'upward' crossover transitions
    - Captures the candle with lowest 'low' price in the period

    Args:
        df (pd.DataFrame): DataFrame containing crossover signals and price data
        crossover_column (str): Column name containing crossover signals

    Returns:
        pd.DataFrame: DataFrame with added supply_zone and demand_zone columns
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = [crossover_column, 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        print(f"  ✗ Required columns not found for zone detection: {missing_columns}")
        df_copy['Zones_sup_dem'] = ''
        return df_copy

    # Initialize zone columns
    df_copy['Zones_sup_dem'] = ''

    try:
        # Get crossover signals
        signals = df_copy[crossover_column]

        # Find crossover indices
        upward_indices = df_copy[signals == 'upward'].index.tolist()
        downward_indices = df_copy[signals == 'downward'].index.tolist()

        supply_zones_count = 0
        demand_zones_count = 0

        # Detect Supply Zones (upward → downward transitions)
        # Iterate through upward crossovers
        for upward_idx in upward_indices:
            # Find the next downward crossover after this upward crossover
            next_downward_indices = [idx for idx in downward_indices if idx > upward_idx]

            if next_downward_indices:
                # Get the first downward crossover after the current upward one
                downward_idx = next_downward_indices[0]

                # Ensure the range is valid (upward_idx <= downward_idx)
                if upward_idx <= downward_idx:
                    # Find the candle with highest 'high' between upward and the next downward crossover
                    zone_data = df_copy.loc[upward_idx:downward_idx]
                    if not zone_data.empty:
                        max_high_idx = zone_data['high'].idxmax()
                        df_copy.loc[max_high_idx, 'Zones_sup_dem'] = 'Supply'
                        supply_zones_count += 1

        # Detect Demand Zones (downward → upward transitions)
        # Iterate through downward crossovers
        for downward_idx in downward_indices:
            # Find the next upward crossover after this downward crossover
            next_upward_indices = [idx for idx in upward_indices if idx > downward_idx]

            if next_upward_indices:
                # Get the first upward crossover after the current downward one
                upward_idx = next_upward_indices[0]

                # Ensure the range is valid (downward_idx <= upward_idx)
                if downward_idx <= upward_idx:
                    # Find the candle with lowest 'low' between downward and the next upward crossover
                    zone_data = df_copy.loc[downward_idx:upward_idx]
                    if not zone_data.empty:
                        min_low_idx = zone_data['low'].idxmin()
                        df_copy.loc[min_low_idx, 'Zones_sup_dem'] = 'Demand'
                        demand_zones_count += 1

        # print(f"  ✓ Detected {supply_zones_count} supply zones and {demand_zones_count} demand zones") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting supply/demand zones: {str(e)}")

    return df_copy

def validate_zones(df):
    """
    Initialize zone status for all identified Supply and Demand zones.

    Args:
        df (pd.DataFrame): DataFrame with already identified Supply and Demand zones

    Returns:
        pd.DataFrame: DataFrame with added 'Zone_Status' column
    """
    df_copy = df.copy()

    # Check if the zones column exists
    if 'Zones_sup_dem' not in df_copy.columns:
        print("  ✗ Zones_sup_dem column not found for zone validation")
        df_copy['Zone_Status'] = ''
        return df_copy

    # Initialize Zone_Status column
    df_copy['Zone_Status'] = ''

    try:
        # Set status to 'Valid' for all identified zones
        zone_mask = (df_copy['Zones_sup_dem'] == 'Supply') | (df_copy['Zones_sup_dem'] == 'Demand')
        df_copy.loc[zone_mask, 'Zone_Status'] = 'Valid'

        # Count initialized zones
        valid_zones_count = zone_mask.sum()
        print(f"  ✓ Initialized {valid_zones_count} zones with 'Valid' status")

    except Exception as e:
        print(f"  ✗ Error validating zones: {str(e)}")

    return df_copy


def update_supply_zone_status(df):
    """
    Update the status of Supply zones based on subsequent price action.

    For each Supply zone with 'Valid' status:
    - If any subsequent candle has high > supply zone high: mark as 'Invalid'
    - Else if any subsequent candle has high >= supply zone low: mark as 'Tested'

    Args:
        df (pd.DataFrame): DataFrame with Supply zones and Zone_Status column

    Returns:
        pd.DataFrame: DataFrame with updated zone statuses
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        print(f"  ✗ Required columns not found for supply zone status update: {missing_columns}")
        return df_copy

    try:
        # Find all Supply zones with 'Valid' status
        supply_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Supply') &
                              (df_copy['Zone_Status'] == 'Valid')].index.tolist()

        updated_count = 0

        for zone_idx in supply_zones:
            zone_high = df_copy.loc[zone_idx, 'high']
            zone_low = df_copy.loc[zone_idx, 'low']

            # Get all subsequent candles
            subsequent_candles = df_copy.loc[zone_idx + 1:]

            if not subsequent_candles.empty:
                # Check for invalidation (high > zone_high)
                invalidation_candles = subsequent_candles[subsequent_candles['high'] > zone_high]

                if not invalidation_candles.empty:
                    df_copy.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                    updated_count += 1
                else:
                    # Check for testing (high >= zone_low)
                    test_candles = subsequent_candles[subsequent_candles['high'] >= zone_low]

                    if not test_candles.empty:
                        df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
                        updated_count += 1

        print(f"  ✓ Updated {updated_count} Supply zone statuses")

    except Exception as e:
        print(f"  ✗ Error updating supply zone statuses: {str(e)}")

    return df_copy


def update_demand_zone_status(df):
    """
    Update the status of Demand zones based on subsequent price action.

    For each Demand zone with 'Valid' status:
    - If any subsequent candle has low < demand zone low: mark as 'Invalid'
    - Else if any subsequent candle has low <= demand zone high: mark as 'Tested'

    Args:
        df (pd.DataFrame): DataFrame with Demand zones and Zone_Status column

    Returns:
        pd.DataFrame: DataFrame with updated zone statuses
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        print(f"  ✗ Required columns not found for demand zone status update: {missing_columns}")
        return df_copy

    try:
        # Find all Demand zones with 'Valid' status
        demand_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Demand') &
                              (df_copy['Zone_Status'] == 'Valid')].index.tolist()

        updated_count = 0

        for zone_idx in demand_zones:
            zone_high = df_copy.loc[zone_idx, 'high']
            zone_low = df_copy.loc[zone_idx, 'low']

            # Get all subsequent candles
            subsequent_candles = df_copy.loc[zone_idx + 1:]

            if not subsequent_candles.empty:
                # Check for invalidation (low < zone_low)
                invalidation_candles = subsequent_candles[subsequent_candles['low'] < zone_low]

                if not invalidation_candles.empty:
                    df_copy.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                    updated_count += 1
                else:
                    # Check for testing (low <= zone_high)
                    test_candles = subsequent_candles[subsequent_candles['low'] <= zone_high]

                    if not test_candles.empty:
                        df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
                        updated_count += 1

        print(f"  ✓ Updated {updated_count} Demand zone statuses")

    except Exception as e:
        print(f"  ✗ Error updating demand zone statuses: {str(e)}")

    return df_copy


def process_dataframe(df):
    """
    Processes a DataFrame with WMA calculations, crossover, zone detection, and zone status tracking.

    Args:
        df (pd.DataFrame): Input dataframe with price data.

    Returns:
        pd.DataFrame: Processed DataFrame.
    """
    if df is None or df.empty:
        print("  ✗ Input DataFrame is empty or None.")
        return None

    print(f"  → Processing DataFrame with {len(df)} rows...")

    # Calculate WMAs
    df_processed = calculate_wma(df)

    # Detect crossovers
    df_processed = detect_crossovers(df_processed)

    # Detect supply and demand zones
    df_processed = detect_supply_demand_zones(df_processed)

    # Initialize zone statuses
    df_processed = validate_zones(df_processed)

    # Update supply zone statuses
    df_processed = update_supply_zone_status(df_processed)

    # Update demand zone statuses
    df_processed = update_demand_zone_status(df_processed)

    print("  ✓ DataFrame processing complete.")
    return df_processed