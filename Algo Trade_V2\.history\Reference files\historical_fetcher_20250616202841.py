# historical_fetcher_v2.py
# Fetch past 5 trading days of intraday data using DhanHQ 2.0 compatible API (with from_date/to_date)

import os
import datetime
import pandas as pd
# from Dhan_Tradehull import Tradehull  # or replace with correct DhanHQ SDK import
# from dhanhq import dhanhq  # Ensure you have the correct DhanHQ SDK installed

# === Configuration ===
# client_code = "1105577608"
# token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"  # Replace with valid token

import requests
import datetime
import pandas as pd

token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
# historical_fetcher_v2.py
# Fetch past intraday data from Dhan API and save as structured CSV
# historical_fetcher_v2.py
# Fetch intraday data from Dhan API and save as clean CSV




security_id = "13"  # NIFTY
exchange_segment = "IDX_I"
instrument_type = "INDEX"
interval = "60"  # Options: '1', '5', '15', '25', '60'

from_date = "2025-05-01 09:15:00"
to_date = datetime.datetime.now().strftime("%Y-%m-%d 15:29:00")

# === Request Setup ===

headers = {
    "access-token": token,
    "Content-Type": "application/json"
}

payload = {
    "securityId": security_id,
    "exchangeSegment": exchange_segment,
    "instrument": instrument_type,
    "interval": interval,
    "oi": False,
    "fromDate": from_date,
    "toDate": to_date
}

url = "https://api.dhan.co/v2/charts/intraday"

# === Fetch data ===
resp = requests.post(url, json=payload, headers=headers)
data = resp.json()

# Print the response to understand the structure
print("API Response:")
print(data)
print("\nResponse keys:", list(data.keys()) if isinstance(data, dict) else "Not a dictionary")

# Check if the response is successful and has the expected structure
if isinstance(data, dict) and "data" in data:
    chart_data = data["data"]
    print("\nChart data keys:", list(chart_data.keys()) if isinstance(chart_data, dict) else "Chart data is not a dictionary")

    # Create DataFrame from individual lists
    df = pd.DataFrame({
        "open": chart_data["open"],
        "high": chart_data["high"],
        "low": chart_data["low"],
        "close": chart_data["close"],
        "volume": chart_data["volume"],
        "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
    })
elif isinstance(data, dict) and all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
    # Data is directly in the response
    df = pd.DataFrame({
        "open": data["open"],
        "high": data["high"],
        "low": data["low"],
        "close": data["close"],
        "volume": data["volume"],
        "timestamp": pd.to_datetime(data["timestamp"], unit="s", utc=True)
    })
else:
    print("❌ Unexpected response structure. Cannot create DataFrame.")
    exit(1)

# Convert timestamp to IST (UTC+5:30)
IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
# Convert to IST and overwrite 'timestamp'
df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

# Reorder columns: IST timestamp first
df = df[["timestamp", "open", "high", "low", "close", "volume"]]

# Save to CSV
df.to_csv(f"NIFTY_INDEX_{interval}min_{from_date[:10]}_to_{to_date[:10]}.csv", index=False)

# Output preview
print("✅ Data fetched from {} to {}".format(from_date, to_date))
print(df.head(10))
print(df.tail(10))




# # === Handle response ===
# if data.get("status") == "success" and isinstance(data.get("data"), dict):
#     raw = data["open", "high", "low", "close", "volume", "timestamp"]  # Extract relevant fields
#     try:
#         df = pd.DataFrame(raw)

#         # Convert UNIX timestamp to datetime
#         df["timestampdate"] = pd.to_datetime(df["timestamp"], unit="s")

#         # Reorder columns
#         df = df[["timestampdate", "open", "high", "low", "close", "volume"]]

#         # Create output folder and path
#         os.makedirs("Historical_Data_Layer", exist_ok=True)
#         filename = f"NIFTY_INDEX_{interval}min_{from_date[:10]}_to_{to_date[:10]}.csv"
#         path = os.path.join("Historical_Data_Layer", filename)

#         # Save to CSV
#         df.to_csv(path, index=False)
#         print(f"✅ Data saved to {path}")
#         print(f"📊 Rows: {len(df)}")

#     except Exception as e:
#         print("❌ Error converting response to DataFrame:", e)

# else:
#     print("❌ Failed to fetch data from Dhan API.")
#     print("🔍 Full response:", data)
