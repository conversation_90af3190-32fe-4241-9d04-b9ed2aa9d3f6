#!/usr/bin/env python3
"""
Test script for the new zone status tracking functionality.
"""

import sys
import os
sys.path.append('Reference files')

from processing_functions import process_dataframe
import pandas as pd

def test_zone_status_functionality():
    """Test the new zone status tracking functionality."""
    print('Testing the updated processing_functions.py...')
    
    try:
        # Load a small sample of data
        df = pd.read_csv('NIFTY_INDEX_5min_2025-06-05_to_2025-06-16.csv')
        print(f'Loaded data with {len(df)} rows')
        
        # Process only first 100 rows for testing
        df_sample = df.head(100).copy()
        print(f'Processing sample with {len(df_sample)} rows...')
        
        # Process the data
        result = process_dataframe(df_sample)
        
        if result is not None:
            print(f'Processing successful! Result has {len(result)} rows')
            print(f'Columns: {list(result.columns)}')
            
            # Check if Zone_Status column was added
            if 'Zone_Status' in result.columns:
                print('✓ Zone_Status column successfully added')
                
                # Show zone status distribution
                zone_status_counts = result['Zone_Status'].value_counts()
                print(f'Zone status distribution: {dict(zone_status_counts)}')
                
                # Show some examples of zones with their statuses
                zones_with_status = result[result['Zones_sup_dem'].isin(['Supply', 'Demand'])]
                if not zones_with_status.empty:
                    print(f'Found {len(zones_with_status)} zones with statuses:')
                    for idx, row in zones_with_status.iterrows():
                        print(f'  Row {idx}: {row["Zones_sup_dem"]} zone with status "{row["Zone_Status"]}"')
                        print(f'    High: {row["high"]}, Low: {row["low"]}')
                else:
                    print('No zones found in sample data')
                    
                # Test with more data to see zone status changes
                print('\nTesting with larger dataset...')
                df_large = df.head(300).copy()
                result_large = process_dataframe(df_large)
                
                if result_large is not None:
                    zones_large = result_large[result_large['Zones_sup_dem'].isin(['Supply', 'Demand'])]
                    if not zones_large.empty:
                        print(f'Found {len(zones_large)} zones in larger dataset:')
                        status_counts_large = zones_large['Zone_Status'].value_counts()
                        print(f'Status distribution: {dict(status_counts_large)}')
                        
                        # Show examples of each status
                        for status in ['Valid', 'Tested', 'Invalid']:
                            status_zones = zones_large[zones_large['Zone_Status'] == status]
                            if not status_zones.empty:
                                print(f'\n{status} zones:')
                                for idx, row in status_zones.head(3).iterrows():
                                    print(f'  Row {idx}: {row["Zones_sup_dem"]} zone - High: {row["high"]}, Low: {row["low"]}')
                    
            else:
                print('✗ Zone_Status column not found')
        else:
            print('✗ Processing failed')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_zone_status_functionality()
