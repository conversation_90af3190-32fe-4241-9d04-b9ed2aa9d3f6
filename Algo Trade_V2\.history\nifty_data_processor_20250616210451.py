#!/usr/bin/env python3
"""
Nifty Data Processor

This script combines the functionality of historical data fetching and WMA processing:
1. Fetches historical data for different intervals (1min, 5min, 60min, daily)
2. Calculates Weighted Moving Averages (WMA) for periods: 5, 10, 45, 65, 90
3. Detects crossovers between WMA5 and WMA10
4. Detects supply and demand zones based on crossover transitions
5. Saves both raw and processed data

Requirements:
- pandas
- pandas_ta
- dhanhq
- requests
"""

import os
import datetime
import pandas as pd
import pandas_ta as ta
import numpy as np
import requests
from dhanhq import dhanhq
import warnings
from pathlib import Path

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configuration
TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
SECURITY_ID = "13"  # NIFTY
EXCHANGE_SEGMENT = "IDX_I"
INSTRUMENT_TYPE = "INDEX"

def fetch_historical_data(interval="60"):
    """
    Fetch historical data from Dhan API based on interval
    Intervals:
    - "1": 1 minute (5 days)
    - "5": 5 minutes (10 days)
    - "60": 60 minutes (30 days)
    - "daily": Daily data (1 year)
    """
    now = datetime.datetime.now()
    today = now.date()
    
    current_time = now.time()
    market_close = datetime.time(15, 29)
    
    if current_time > market_close:
        end_date = today
    else:
        end_date = today - datetime.timedelta(days=1)
        while end_date.weekday() > 4:  # Skip weekends
            end_date = end_date - datetime.timedelta(days=1)
    
    # Calculate from_date based on interval
    if interval == "1":
        from_date = (end_date - datetime.timedelta(days=5)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "5":
        from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "60":
        from_date = (end_date - datetime.timedelta(days=30)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "daily":
        from_date = (end_date - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    to_date = end_date.strftime("%Y-%m-%d 15:29:00") if interval != "daily" else end_date.strftime("%Y-%m-%d")
    
    print(f"Fetching {interval} interval data from {from_date} to {to_date}")
    
    if interval == "daily":
        client = dhanhq("1105577608", TOKEN)
        data = client.historical_daily_data(
            security_id=SECURITY_ID,
            exchange_segment=EXCHANGE_SEGMENT,
            instrument_type=INSTRUMENT_TYPE,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
    else:
        headers = {
            "access-token": TOKEN,
            "Content-Type": "application/json"
        }
        payload = {
            "securityId": SECURITY_ID,
            "exchangeSegment": EXCHANGE_SEGMENT,
            "instrument": INSTRUMENT_TYPE,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()
    
    # Convert to DataFrame
    if interval == "daily":
        df = pd.DataFrame(data)
        # Rename columns to match expected names
        column_map = {
            'Symbol': 'symbol',
            'CLOSE_PRICE': 'close',
            'OPEN_PRICE': 'open',
            'HIGH_PRICE': 'high',
            'LOW_PRICE': 'low',
            'LTQ': 'volume',
            'AVG_PRICE': 'average',
            'TIME_STAMP': 'datetime'
        }
        df = df.rename(columns=column_map)
    else:
        df = pd.DataFrame(data.get('data', []))
    
    # Save raw data
    filename = f"NIFTY_INDEX_{interval}{'min' if interval != 'daily' else ''}_{from_date[:10]}_to_{to_date[:10]}.csv"
    df.to_csv(filename, index=False)
    print(f"Saved raw data to {filename}")
    
    return df

def calculate_wma(df, price_column='close', periods=[5, 10, 45, 65, 90]):
    """
    Calculate Weighted Moving Averages for specified periods.
    """
    df_copy = df.copy()
    
    if price_column not in df_copy.columns:
        raise ValueError(f"Price column '{price_column}' not found in dataframe")
    
    for period in periods:
        try:
            wma_values = ta.wma(df_copy[price_column], length=period)
            df_copy[f'WMA{period}'] = wma_values
            print(f"  ✓ Calculated WMA{period}")
        except Exception as e:
            print(f"  ✗ Error calculating WMA{period}: {str(e)}")
            df_copy[f'WMA{period}'] = np.nan
    
    return df_copy

def detect_crossovers(df, fast_wma='WMA5', slow_wma='WMA10'):
    """
    Detect crossovers between two WMA series.
    """
    df_copy = df.copy()
    
    if fast_wma == 'WMA5' and slow_wma == 'WMA10':
        crossover_signal_name = 'crossover_signal'
    else:
        crossover_signal_name = f'crossover_signal_{fast_wma}_{slow_wma}'
    
    if fast_wma not in df_copy.columns or slow_wma not in df_copy.columns:
        print(f"  ✗ Required WMA columns not found for crossover detection")
        df_copy[crossover_signal_name] = 'neutral'
        return df_copy
    
    df_copy[crossover_signal_name] = 'neutral'
    
    try:
        fast_above_slow = df_copy[fast_wma] > df_copy[slow_wma]
        crossover_up = fast_above_slow & ~fast_above_slow.shift(1)
        crossover_down = ~fast_above_slow & fast_above_slow.shift(1)
        
        df_copy.loc[crossover_up, crossover_signal_name] = 'upward'
        df_copy.loc[crossover_down, crossover_signal_name] = 'downward'
        print(f"  ✓ Detected crossovers between {fast_wma} and {slow_wma}")
    except Exception as e:
        print(f"  ✗ Error detecting crossovers: {str(e)}")
    
    return df_copy

def detect_zones(df):
    """
    Detect supply and demand zones based on crossover transitions.
    """
    df_copy = df.copy()
    
    # Initialize zone columns
    df_copy['supply_zone'] = np.nan
    df_copy['demand_zone'] = np.nan
    
    try:
        # Find transitions in crossover signals
        signal_changes = df_copy['crossover_signal'] != df_copy['crossover_signal'].shift(1)
        transition_points = df_copy[signal_changes].index
        
        for i in range(len(transition_points) - 1):
            current_idx = transition_points[i]
            next_idx = transition_points[i + 1]
            current_signal = df_copy.loc[current_idx, 'crossover_signal']
            next_signal = df_copy.loc[next_idx, 'crossover_signal']
            
            segment = df_copy.loc[current_idx:next_idx]
            
            if current_signal == 'upward' and next_signal == 'downward':
                # Supply zone: highest high in the upward to downward transition
                supply_price = segment['high'].max()
                supply_idx = segment.loc[segment['high'] == supply_price].index[0]
                df_copy.loc[supply_idx, 'supply_zone'] = supply_price
            
            elif current_signal == 'downward' and next_signal == 'upward':
                # Demand zone: lowest low in the downward to upward transition
                demand_price = segment['low'].min()
                demand_idx = segment.loc[segment['low'] == demand_price].index[0]
                df_copy.loc[demand_idx, 'demand_zone'] = demand_price
        
        print("  ✓ Detected supply and demand zones")
    except Exception as e:
        print(f"  ✗ Error detecting zones: {str(e)}")
    
    return df_copy

def process_data(df, interval):
    """
    Process the data by calculating WMAs and detecting patterns.
    """
    print(f"\nProcessing data for {interval} interval...")
    
    # Calculate WMAs
    df = calculate_wma(df)
    
    # Detect crossovers
    df = detect_crossovers(df)
    
    # Detect zones
    df = detect_zones(df)
    
    # Save processed data
    output_dir = "processed_files"
    os.makedirs(output_dir, exist_ok=True)
    
    filename = f"NIFTY_INDEX_{interval}{'min' if interval != 'daily' else ''}_{df['timestamp'].iloc[0][:10]}_to_{df['timestamp'].iloc[-1][:10]}_processed.csv"
    output_path = os.path.join(output_dir, filename)
    df.to_csv(output_path, index=False)
    print(f"Saved processed data to {output_path}")
    
    return df

def main():
    """
    Main function to fetch and process data for all intervals.
    """
    intervals = ["1", "5", "60", "daily"]
    
    for interval in intervals:
        try:
            print(f"\nProcessing {interval} interval data...")
            # Fetch historical data
            df = fetch_historical_data(interval)
            
            # Process the data
            if not df.empty:
                processed_df = process_data(df, interval)
                print(f"Successfully processed {interval} interval data")
            else:
                print(f"No data received for {interval} interval")
        except Exception as e:
            print(f"Error processing {interval} interval data: {str(e)}")

if __name__ == "__main__":
    main()
