#!/usr/bin/env python3
"""
WMA Processor Script

This script processes CSV files from the "Reference files" directory and:
1. Calculates Weighted Moving Averages (WMA) for periods: 5, 10, 45, 65, 90
2. Detects crossovers between WMA5 and WMA10
3. Detects supply and demand zones based on crossover transitions
4. Preserves all original data and adds new columns
5. Outputs processed files with clear naming

Supply Zones: Identified between 'upward' → 'downward' crossover transitions
- Captures candle with highest 'high' price in the transition period

Demand Zones: Identified between 'downward' → 'upward' crossover transitions
- Captures candle with lowest 'low' price in the transition period

Requirements:
- pandas
- pandas_ta

Author: AI Assistant
Date: 2025
"""

import pandas as pd
import pandas_ta as ta
import os
import glob
import numpy as np
from pathlib import Path
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')


def calculate_wma(df, price_column='close', periods=[5, 10, 45, 65, 90]):
    """
    Calculate Weighted Moving Averages for specified periods using pandas_ta.
    
    Args:
        df (pd.DataFrame): Input dataframe with price data
        price_column (str): Column name to use for WMA calculation (default: 'close')
        periods (list): List of periods for WMA calculation
    
    Returns:
        pd.DataFrame: DataFrame with added WMA columns
    """
    df_copy = df.copy()
    
    # Ensure the price column exists
    if price_column not in df_copy.columns:
        raise ValueError(f"Price column '{price_column}' not found in dataframe")
    
    # Calculate WMA for each period
    for period in periods:
        try:
            # Use pandas_ta to calculate WMA
            wma_values = ta.wma(df_copy[price_column], length=period)
            df_copy[f'WMA{period}'] = wma_values
            print(f"  ✓ Calculated WMA{period}")
        except Exception as e:
            print(f"  ✗ Error calculating WMA{period}: {str(e)}")
            df_copy[f'WMA{period}'] = np.nan
    
    return df_copy


def detect_crossovers(df, fast_wma='WMA5', slow_wma='WMA10'):
    """
    Detect crossovers between two WMA series.

    Args:
        df (pd.DataFrame): DataFrame containing WMA columns
        fast_wma (str): Column name for fast WMA (default: 'WMA5')
        slow_wma (str): Column name for slow WMA (default: 'WMA10')

    Returns:
        pd.DataFrame: DataFrame with added crossover signal column
    """
    df_copy = df.copy()

    # Use standard column name for WMA5/WMA10 crossovers, custom name for others
    if fast_wma == 'WMA5' and slow_wma == 'WMA10':
        crossover_signal_name = 'crossover_signal'
    else:
        crossover_signal_name = f'crossover_signal_{fast_wma}_{slow_wma}'

    # Check if required columns exist
    if fast_wma not in df_copy.columns or slow_wma not in df_copy.columns:
        print(f"  ✗ Required WMA columns not found for crossover detection")
        df_copy[crossover_signal_name] = 'neutral'
        return df_copy

    # Initialize crossover signal column
    df_copy[crossover_signal_name] = 'neutral'

    # Calculate crossover signals
    try:
        # Get the WMA series
        fast_series = df_copy[fast_wma]
        slow_series = df_copy[slow_wma]

        # Calculate the difference and its shift
        diff = fast_series - slow_series
        diff_prev = diff.shift(1)

        # Detect crossovers
        # Upward crossover: fast WMA crosses above slow WMA
        upward_cross = (diff > 0) & (diff_prev <= 0)

        # Downward crossover: fast WMA crosses below slow WMA
        downward_cross = (diff < 0) & (diff_prev >= 0)

        # Apply signals
        df_copy.loc[upward_cross, crossover_signal_name] = 'upward'
        df_copy.loc[downward_cross, crossover_signal_name] = 'downward'

        # Count crossovers
        upward_count = upward_cross.sum()
        downward_count = downward_cross.sum()

        print(f"  ✓ Detected {upward_count} upward and {downward_count} downward crossovers")

    except Exception as e:
        print(f"  ✗ Error detecting crossovers: {str(e)}")

    return df_copy


def detect_supply_demand_zones(df, crossover_column='crossover_signal'):
    """
    Detect supply and demand zones based on crossover transitions.

    Supply Zones: Price ranges between 'upward' → 'downward' crossover transitions
    - Captures the candle with highest 'high' price in the period

    Demand Zones: Price ranges between 'downward' → 'upward' crossover transitions
    - Captures the candle with lowest 'low' price in the period

    Args:
        df (pd.DataFrame): DataFrame containing crossover signals and price data
        crossover_column (str): Column name containing crossover signals

    Returns:
        pd.DataFrame: DataFrame with added supply_zone and demand_zone columns
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = [crossover_column, 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        print(f"  ✗ Required columns not found for zone detection: {missing_columns}")
        df_copy['Zones_sup_dem'] = ''
        # df_copy['demand_zone'] = ''
        return df_copy

    # Initialize zone columns
    df_copy['Zones_sup_dem'] = ''
    # df_copy['demand_zone'] = ''

    try:
        # Get crossover signals
        signals = df_copy[crossover_column]

        # Find crossover indices
        upward_indices = df_copy[signals == 'upward'].index.tolist()
        downward_indices = df_copy[signals == 'downward'].index.tolist()

        supply_zones_count = 0
        demand_zones_count = 0

        # Detect Supply Zones (upward → downward transitions)
        for upward_idx in upward_indices:
            # Find the next downward crossover after this upward crossover
            next_downward = [idx for idx in downward_indices if idx > upward_idx]

            if next_downward:
                downward_idx = next_downward[0]

                # Find the candle with highest 'high' between upward and downward crossovers
                zone_data = df_copy.loc[upward_idx:downward_idx]
                if not zone_data.empty:
                    max_high_idx = zone_data['high'].idxmax()
                    df_copy.loc[max_high_idx, 'Zones_sup_dem'] = 'Supply'
                    supply_zones_count += 1

        # Detect Demand Zones (downward → upward transitions)
        for downward_idx in downward_indices:
            # Find the next upward crossover after this downward crossover
            next_upward = [idx for idx in upward_indices if idx > downward_idx]

            if next_upward:
                upward_idx = next_upward[0]

                # Find the candle with lowest 'low' between downward and upward crossovers
                zone_data = df_copy.loc[downward_idx:upward_idx]
                if not zone_data.empty:
                    min_low_idx = zone_data['low'].idxmin()
                    df_copy.loc[min_low_idx, 'Zones_sup_dem'] = 'Demand'
                    demand_zones_count += 1

        print(f"  ✓ Detected {supply_zones_count} supply zones and {demand_zones_count} demand zones")

    except Exception as e:
        print(f"  ✗ Error detecting supply/demand zones: {str(e)}")

    return df_copy


def process_csv_file(file_path, output_dir="processed_files"):
    """
    Process a single CSV file with WMA calculations and crossover detection.
    
    Args:
        file_path (str): Path to the input CSV file
        output_dir (str): Directory to save processed files
    
    Returns:
        bool: True if processing successful, False otherwise
    """
    try:
        print(f"\nProcessing: {os.path.basename(file_path)}")
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        print(f"  ✓ Loaded {len(df)} rows and {len(df.columns)} columns")
        
        # Display column information
        print(f"  ✓ Columns: {list(df.columns)}")
        
        # Check if 'close' column exists (required for WMA calculation)
        if 'close' not in df.columns:
            print(f"  ✗ 'close' column not found. Available columns: {list(df.columns)}")
            return False
        
        # Calculate WMAs
        print("  → Calculating WMAs...")
        df_processed = calculate_wma(df)
        
        # Detect crossovers
        print("  → Detecting crossovers...")
        df_processed = detect_crossovers(df_processed)

        # Detect supply and demand zones
        print("  → Detecting supply and demand zones...")
        df_processed = detect_supply_demand_zones(df_processed)

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate output filename
        input_filename = Path(file_path).stem
        output_filename = f"{input_filename}_processed.csv"
        output_path = os.path.join(output_dir, output_filename)
        
        # Save processed file
        df_processed.to_csv(output_path, index=False)
        print(f"  ✓ Saved processed file: {output_path}")
        
        # Display summary statistics
        print(f"  ✓ Final dataset: {len(df_processed)} rows, {len(df_processed.columns)} columns")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Error processing {file_path}: {str(e)}")
        return False


def main():
    """
    Main function to process all CSV files in the Reference files directory.
    """
    print("=" * 60)
    print("WMA PROCESSOR - CSV File Processing Tool")
    print("=" * 60)
    
    # Define input directory
    input_dir = "Reference files"
    
    # Check if input directory exists
    if not os.path.exists(input_dir):
        print(f"✗ Input directory '{input_dir}' not found!")
        return
    
    # Find all CSV files in the input directory
    csv_pattern = os.path.join(input_dir, "*.csv")
    csv_files = glob.glob(csv_pattern)
    
    if not csv_files:
        print(f"✗ No CSV files found in '{input_dir}' directory!")
        return
    
    print(f"✓ Found {len(csv_files)} CSV file(s) to process:")
    for file in csv_files:
        print(f"  - {os.path.basename(file)}")
    
    # Process each CSV file
    successful_files = 0
    failed_files = 0
    
    for csv_file in csv_files:
        if process_csv_file(csv_file):
            successful_files += 1
        else:
            failed_files += 1
    
    # Print summary
    print("\n" + "=" * 60)
    print("PROCESSING SUMMARY")
    print("=" * 60)
    print(f"✓ Successfully processed: {successful_files} files")
    print(f"✗ Failed to process: {failed_files} files")
    print(f"📁 Output directory: processed_files/")
    
    if successful_files > 0:
        print("\n✓ Processing completed successfully!")
        print("📊 New columns added to each file:")
        print("   - WMA5, WMA10, WMA45, WMA65, WMA90")
        print("   - crossover_signal (upward/downward/neutral)")
        print("   - supply_zone (Supply or empty)")
        print("   - demand_zone (Demand or empty)")
    else:
        print("\n✗ No files were processed successfully.")


if __name__ == "__main__":
    main()
