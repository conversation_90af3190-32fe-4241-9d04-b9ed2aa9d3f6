import pandas as pd
import os
from pathlib import Path
import datetime

# Import the fetcher function
# Adjust the import path as necessary based on your project structure
# Assuming historical_fetcher.py is in the same directory
# from historical_fetcher import fetch_historical_data # Uncomment this line in a real scenario

# Import the processing function
# Assuming processing_functions.py is in the same directory
from processing_functions import process_dataframe

def main():
    """
    Main function to fetch data and process it with WMA calculations and zone detection.
    """
    print("=" * 60)
    print("DATA FETCHING AND WMA PROCESSING WORKFLOW")
    print("=" * 60)

    # Define intervals to process
    intervals_to_process = ["1", "5", "60", "daily"]
    output_dir = "processed_files"

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    successful_workflows = 0
    failed_workflows = 0

    for interval in intervals_to_process:
        print(f"\n--- Starting workflow for interval: {interval}min ---")

        # Step 1: Fetch historical data
        # In a real scenario, you would call the actual fetch_historical_data function
        # from historical_fetcher.py here.
        # Assuming fetch_historical_data returns (df, from_date_str, to_date_str)
        # Example call:
        # fetched_data, from_date_str, to_date_str = fetch_historical_data(interval)

        # --- Simulation based on provided context files for this response ---
        # This part simulates loading the data from the CSVs provided in the context.
        # Replace this simulation with the actual call to fetch_historical_data
        # in your real application.
        def simulate_load_fetched_data(interval):
            """
            Simulates loading data from the CSV files generated by historical_fetcher.py.
            """
            print(f"  → Loading data from simulated fetch for interval: {interval}min")
            # Map intervals to the provided context file paths
            file_map = {
                "1": "/Volumes/Kaundinya1TB/docs/Stocks and Shares/Algo Trade_V1/Reference files/NIFTY_INDEX_1min_2025-06-11_to_2025-06-16.csv",
                "5": "/Volumes/Kaundinya1TB/docs/Stocks and Shares/Algo Trade_V1/Reference files/NIFTY_INDEX_5min_2025-06-06_to_2025-06-16.csv",
                "60": "/Volumes/Kaundinya1TB/docs/Stocks and Shares/Algo Trade_V1/Reference files/NIFTY_INDEX_60min_2025-05-17_to_2025-06-16.csv",
                "daily": "/Volumes/Kaundinya1TB/docs/Stocks and Shares/Algo Trade_V1/Reference files/NIFTY_INDEX_daily_2024-06-16_to_2025-06-16.csv"
            }
            file_path = file_map.get(interval)

            if not file_path or not os.path.exists(file_path):
                print(f"  ✗ Simulated file not found for interval {interval}: {file_path}")
                return None, None, None

            try:
                df = pd.read_csv(file_path)
                # Extract dates from filename for output naming
                parts = Path(file_path).stem.split('_')
                from_date_str = parts[-3]
                to_date_str = parts[-1]
                print(f"  ✓ Loaded data from {file_path}")
                return df, from_date_str, to_date_str
            except Exception as e:
                print(f"  ✗ Error loading simulated data from {file_path}: {str(e)}")
                return None, None, None

        # Use the simulation for this request based on provided context
        fetched_data, from_date_str, to_date_str = simulate_load_fetched_data(interval)

        # --- End Simulation ---


        if fetched_data is not None:
            # Step 2: Process the fetched DataFrame using the function from processing_functions.py
            processed_df = process_dataframe(fetched_data)

            if processed_df is not None:
                # Step 3: Save the processed DataFrame
                try:
                    interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                    output_filename = f"NIFTY_INDEX_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                    output_path = os.path.join(output_dir, output_filename)

                    processed_df.to_csv(output_path, index=False)
                    print(f"  ✓ Saved processed file: {output_path}")
                    successful_workflows += 1
                except Exception as e:
                    print(f"  ✗ Error saving processed data for interval {interval}: {str(e)}")
                    failed_workflows += 1
            else:
                print(f"  ✗ Processing failed for interval: {interval}min")
                failed_workflows += 1
        else:
            print(f"  ✗ Data fetch/load failed for interval: {interval}min")
            failed_workflows += 1

    # Print overall summary
    print("\n" + "=" * 60)
    print("OVERALL WORKFLOW SUMMARY")
    print("=" * 60)
    print(f"✓ Successfully completed workflows: {successful_workflows}")
    print(f"✗ Failed workflows: {failed_workflows}")
    print(f"📁 Output directory: {output_dir}/")

    if successful_workflows > 0:
        print("\n✓ Workflow completed successfully!")
    else:
        print("\n✗ No workflows were completed successfully.")


if __name__ == "__main__":
    main()