# historical_fetcher_v2.py
# Fetch past 5 trading days of intraday data using DhanHQ 2.0 compatible API (with from_date/to_date)

import os
import datetime
import pandas as pd
# from Dhan_Tradehull import Tradehull  # or replace with correct DhanHQ SDK import
# from dhanhq import dhanhq  # Ensure you have the correct DhanHQ SDK installed

# === Configuration ===
# client_code = "1105577608"
# token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"  # Replace with valid token

import requests
import datetime
import pandas as pd
from dhanhq import dhanhq

token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

def fetch_historical_data(interval="60"):
    """
    Fetch historical data from Dhan API based on interval
    Intervals:
    - "1": 1 minute (5 days)
    - "5": 5 minutes (10 days)
    - "60": 60 minutes (30 days)
    - "daily": Daily data (1 year)
    """
    security_id = "13"  # NIFTY
    exchange_segment = "IDX_I"
    instrument_type = "INDEX"
    
    now = datetime.datetime.now()
    today = now.date()
    
    # If current time is past 15:29, use today's date for end date
    # If current time is before 15:29, use the previous trading day
    current_time = now.time()
    market_close = datetime.time(15, 29)
    
    if current_time > market_close:
        end_date = today
    else:
        # Find the previous trading day
        end_date = today - datetime.timedelta(days=1)
        # Skip weekends
        while end_date.weekday() > 4:  # 5 is Saturday, 6 is Sunday
            end_date = end_date - datetime.timedelta(days=1)
    
    # Calculate from_date based on interval
    if interval == "1":
        from_date = (end_date - datetime.timedelta(days=5)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "5":
        from_date = (end_date - datetime.timedelta(days=10)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "60":
        from_date = (end_date - datetime.timedelta(days=30)).strftime("%Y-%m-%d 09:15:00")
    elif interval == "daily":
        from_date = (end_date - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    # For intraday data, always use 15:29 as the end time
    to_date = end_date.strftime("%Y-%m-%d 15:29:00") if interval != "daily" else end_date.strftime("%Y-%m-%d")

    print("To date validate: ",to_date)

    # === Request Setup ===
    headers = {
        "access-token": token,
        "Content-Type": "application/json"
    }

    if interval == "daily":
        # Initialize DhanHQ client for daily data
        client = dhanhq("1105577608", token)
        res = client.historical_daily_data(
            security_id=security_id,
            exchange_segment=exchange_segment,
            instrument_type=instrument_type,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
        data = res
    else:
        # Setup for intraday data
        payload = {
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "instrument": instrument_type,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()

    # Print the response to understand the structure
    print("API Response:")
    # print(data)
    print("\nResponse keys:", list(data.keys()) if isinstance(data, dict) else "Not a dictionary")

    # Check if the response is successful and has the expected structure
    if isinstance(data, dict):
        if "data" in data and interval != "daily":
            chart_data = data["data"]
            print("\nChart data keys:", list(chart_data.keys()) if isinstance(chart_data, dict) else "Chart data is not a dictionary")
            
            # Create DataFrame from individual lists
            df = pd.DataFrame({
                "open": chart_data["open"],
                "high": chart_data["high"],
                "low": chart_data["low"],
                "close": chart_data["close"],
                "volume": chart_data["volume"],
                "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
            })
        elif "data" in data and interval == "daily":
            # Handle daily data format
            df = pd.DataFrame(data["data"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
        elif all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
            # Data is directly in the response
            df = pd.DataFrame({
                "open": data["open"],
                "high": data["high"],
                "low": data["low"],
                "close": data["close"],
                "volume": data["volume"],
                "timestamp": pd.to_datetime(data["timestamp"], unit="s", utc=True)
            })
        else:
            print("❌ Unexpected response structure. Cannot create DataFrame.")
            return None

    # Convert timestamp to IST (UTC+5:30)
    IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    # Convert to IST and overwrite 'timestamp'
    df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

    # Reorder columns: IST timestamp first
    df = df[["timestamp", "open", "high", "low", "close", "volume"]]

    # Create filename based on interval
    interval_suffix = "daily" if interval == "daily" else f"{interval}min"
    filename = f"NIFTY_INDEX_{interval_suffix}_{from_date[:10]}_to_{to_date[:10]}.csv"
    
    # Save to CSV
    df.to_csv(filename, index=False)
    
    # Output preview
    print("✅ Data fetched from {} to {}".format(from_date, to_date))
    print(df.head(10))
    print(df.tail(10))
    
    return df

# Example usage
if __name__ == "__main__":
    # Fetch data for each interval
    intervals = ["1", "5", "60", "daily"]
    
    for interval in intervals:
        print(f"\nFetching {interval} data...")
        df = fetch_historical_data(interval)
        if df is not None:
            print(f"✅ Successfully fetched {interval} data with {len(df)} rows")
