# WMA Processor - Trading Analysis Tool

This project provides a comprehensive Python script for processing CSV files containing trading data and calculating Weighted Moving Averages (WMA) with crossover detection.

## Features

- **Automated CSV Processing**: Processes all CSV files in the "Reference files" directory
- **WMA Calculations**: Calculates WMA for periods 5, 10, 45, 65, and 90 using pandas_ta library
- **Crossover Detection**: Detects WMA5/WMA10 crossovers (upward/downward/neutral)
- **Supply & Demand Zones**: Identifies key price zones based on crossover transitions
- **Data Preservation**: Maintains all original data while adding new analytical columns
- **Error Handling**: Robust error handling for various CSV structures
- **Virtual Environment**: Uses `uv` for isolated dependency management

## Setup Instructions

### 1. Virtual Environment Setup

The project uses `uv` package manager for dependency isolation:

```bash
# Create virtual environment
uv venv wma-env2

# Activate virtual environment (Windows)
source wma-env2/Scripts/activate

# Install dependencies
uv pip install "numpy<2.0" "pandas>=1.3.0" "pandas_ta>=0.3.14b" setuptools
```

### 2. Directory Structure

```
project_root/
├── Reference files/           # Input CSV files
│   ├── NIFTY_INDEX_1min_*.csv
│   ├── NIFTY_INDEX_5min_*.csv
│   └── NIFTY_INDEX_60min_*.csv
├── processed_files/           # Output directory (auto-created)
├── wma_processor.py          # Main processing script
├── test_wma_processor.py     # Analysis and testing script
└── README.md                 # This file
```

## Usage

### 1. Process CSV Files

```bash
# Ensure virtual environment is activated
python wma_processor.py
```

This will:
- Read all CSV files from "Reference files/" directory
- Calculate WMA for periods 5, 10, 45, 65, 90
- Detect crossovers between WMA5 and WMA10
- Identify supply and demand zones based on crossover transitions
- Save processed files to "processed_files/" directory

### 2. Analyze Results

```bash
# Run analysis script
python test_wma_processor.py
```

This provides:
- Summary statistics for each processed file
- Crossover signal distribution
- Data integrity verification
- Comparison between original and processed files

## Output Format

### New Columns Added

Each processed file includes the original columns plus:

- **WMA5**: 5-period Weighted Moving Average
- **WMA10**: 10-period Weighted Moving Average  
- **WMA45**: 45-period Weighted Moving Average
- **WMA65**: 65-period Weighted Moving Average
- **WMA90**: 90-period Weighted Moving Average
- **crossover_signal**: Crossover detection result
  - `upward`: WMA5 crosses above WMA10
  - `downward`: WMA5 crosses below WMA10
  - `neutral`: No crossover detected
- **supply_zone**: Supply zone identification
  - `Supply`: Candle with highest high between upward → downward crossover transitions
  - Empty string: Not a supply zone
- **demand_zone**: Demand zone identification
  - `Demand`: Candle with lowest low between downward → upward crossover transitions
  - Empty string: Not a demand zone

### File Naming Convention

Processed files are saved with "_processed" suffix:
- `NIFTY_INDEX_1min_2025-06-01_to_2025-06-13.csv` → `NIFTY_INDEX_1min_2025-06-01_to_2025-06-13_processed.csv`

## Technical Details

### Dependencies

- **pandas**: Data manipulation and analysis
- **pandas_ta**: Technical analysis indicators (WMA calculations)
- **numpy**: Numerical computing (version <2.0 for compatibility)

### WMA Calculation

Uses pandas_ta library exclusively for WMA calculations:
```python
wma_values = ta.wma(df['close'], length=period)
```

### Crossover Detection Algorithm

```python
# Calculate difference between fast and slow WMA
diff = fast_series - slow_series
diff_prev = diff.shift(1)

# Detect crossovers
upward_cross = (diff > 0) & (diff_prev <= 0)
downward_cross = (diff < 0) & (diff_prev >= 0)
```

### Supply & Demand Zone Detection

The enhanced script identifies key price zones based on crossover transitions:

**Supply Zone Detection**:
- Triggered by upward → downward crossover transitions
- Identifies the candle with the highest 'high' price between the upward crossover and subsequent downward crossover
- Marks this candle with 'Supply' in the supply_zone column

**Demand Zone Detection**:
- Triggered by downward → upward crossover transitions
- Identifies the candle with the lowest 'low' price between the downward crossover and subsequent upward crossover
- Marks this candle with 'Demand' in the demand_zone column

**Zone Logic Validation**:
- The test script validates 100% accuracy for zone detection logic
- Ensures zones are correctly placed at extreme price points within transition periods
- Handles edge cases where crossovers don't have corresponding transitions

## Processing Results

### Sample Statistics (from test run):

**1-minute data (3,751 rows)**:
- Upward crossovers: 225 (6.0%)
- Downward crossovers: 224 (6.0%)
- Neutral periods: 3,302 (88.0%)
- Supply zones: 224 (6.0%)
- Demand zones: 224 (6.0%)

**5-minute data (3,229 rows)**:
- Upward crossovers: 195 (6.0%)
- Downward crossovers: 195 (6.0%)
- Neutral periods: 2,839 (87.9%)
- Supply zones: 194 (6.0%)
- Demand zones: 195 (6.0%)

**60-minute data (217 rows)**:
- Upward crossovers: 11 (5.1%)
- Downward crossovers: 12 (5.5%)
- Neutral periods: 194 (89.4%)
- Supply zones: 11 (5.1%)
- Demand zones: 11 (5.1%)

## Error Handling

The script includes comprehensive error handling for:
- Missing CSV files
- Invalid data formats
- Missing required columns
- Calculation errors
- File I/O issues

## Data Integrity

- All original data is preserved
- Row counts remain unchanged
- Original column data integrity verified
- NaN values handled appropriately for insufficient data periods

## Troubleshooting

### Common Issues

1. **Import Error**: Ensure virtual environment is activated and dependencies installed
2. **No CSV Files Found**: Check "Reference files" directory exists and contains CSV files
3. **Missing 'close' Column**: Verify CSV files have required 'close' column for WMA calculation
4. **Permission Errors**: Ensure write permissions for "processed_files" directory

### Dependency Compatibility

- Use numpy version <2.0 for pandas_ta compatibility
- pandas_ta requires setuptools for proper installation

## Future Enhancements

Potential improvements:
- Additional technical indicators
- Configurable WMA periods
- Multiple crossover pair analysis
- Real-time processing capabilities
- Advanced visualization features
