#!/usr/bin/env python3
"""
Manual test to verify the zone status logic works correctly.
"""

import pandas as pd
import numpy as np

# Create sample data that will definitely have zones and status changes
def create_test_data():
    """Create test data with known patterns."""
    data = {
        'timestamp': pd.date_range('2025-01-01 09:00:00', periods=20, freq='5min'),
        'open': [100, 101, 102, 103, 104, 105, 104, 103, 102, 101, 100, 99, 98, 97, 98, 99, 100, 101, 102, 103],
        'high': [101, 102, 103, 104, 105, 106, 105, 104, 103, 102, 101, 100, 99, 98, 99, 100, 101, 102, 103, 104],
        'low': [99, 100, 101, 102, 103, 104, 103, 102, 101, 100, 99, 98, 97, 96, 97, 98, 99, 100, 101, 102],
        'close': [101, 102, 103, 104, 105, 104, 103, 102, 101, 100, 99, 98, 97, 98, 99, 100, 101, 102, 103, 104],
        'volume': [1000] * 20
    }
    return pd.DataFrame(data)

def test_zone_functions():
    """Test the zone status functions manually."""
    print("Creating test data...")
    df = create_test_data()
    
    # Manually add some zones for testing
    df['Zones_sup_dem'] = ''
    df.loc[5, 'Zones_sup_dem'] = 'Supply'  # High point at 106
    df.loc[13, 'Zones_sup_dem'] = 'Demand'  # Low point at 96
    
    print("Test data created:")
    print(df[['timestamp', 'high', 'low', 'Zones_sup_dem']].to_string())
    
    # Test validate_zones function
    print("\n=== Testing validate_zones ===")
    
    # Initialize Zone_Status column
    df['Zone_Status'] = ''
    zone_mask = (df['Zones_sup_dem'] == 'Supply') | (df['Zones_sup_dem'] == 'Demand')
    df.loc[zone_mask, 'Zone_Status'] = 'Valid'
    
    print("After validation:")
    zones = df[df['Zones_sup_dem'].isin(['Supply', 'Demand'])]
    print(zones[['timestamp', 'high', 'low', 'Zones_sup_dem', 'Zone_Status']].to_string())
    
    # Test supply zone status update
    print("\n=== Testing Supply Zone Status Update ===")
    supply_zones = df[(df['Zones_sup_dem'] == 'Supply') & (df['Zone_Status'] == 'Valid')].index.tolist()
    
    for zone_idx in supply_zones:
        zone_high = df.loc[zone_idx, 'high']
        zone_low = df.loc[zone_idx, 'low']
        print(f"Supply zone at index {zone_idx}: High={zone_high}, Low={zone_low}")
        
        # Get subsequent candles
        subsequent_candles = df.loc[zone_idx + 1:]
        print(f"Subsequent candles: {len(subsequent_candles)}")
        
        # Check for invalidation
        invalidation_candles = subsequent_candles[subsequent_candles['high'] > zone_high]
        if not invalidation_candles.empty:
            print(f"Zone invalidated by candle(s) with high > {zone_high}")
            df.loc[zone_idx, 'Zone_Status'] = 'Invalid'
        else:
            # Check for testing
            test_candles = subsequent_candles[subsequent_candles['high'] >= zone_low]
            if not test_candles.empty:
                print(f"Zone tested by candle(s) with high >= {zone_low}")
                df.loc[zone_idx, 'Zone_Status'] = 'Tested'
    
    # Test demand zone status update
    print("\n=== Testing Demand Zone Status Update ===")
    demand_zones = df[(df['Zones_sup_dem'] == 'Demand') & (df['Zone_Status'] == 'Valid')].index.tolist()
    
    for zone_idx in demand_zones:
        zone_high = df.loc[zone_idx, 'high']
        zone_low = df.loc[zone_idx, 'low']
        print(f"Demand zone at index {zone_idx}: High={zone_high}, Low={zone_low}")
        
        # Get subsequent candles
        subsequent_candles = df.loc[zone_idx + 1:]
        print(f"Subsequent candles: {len(subsequent_candles)}")
        
        # Check for invalidation
        invalidation_candles = subsequent_candles[subsequent_candles['low'] < zone_low]
        if not invalidation_candles.empty:
            print(f"Zone invalidated by candle(s) with low < {zone_low}")
            df.loc[zone_idx, 'Zone_Status'] = 'Invalid'
        else:
            # Check for testing
            test_candles = subsequent_candles[subsequent_candles['low'] <= zone_high]
            if not test_candles.empty:
                print(f"Zone tested by candle(s) with low <= {zone_high}")
                df.loc[zone_idx, 'Zone_Status'] = 'Tested'
    
    print("\n=== Final Results ===")
    final_zones = df[df['Zones_sup_dem'].isin(['Supply', 'Demand'])]
    print(final_zones[['timestamp', 'high', 'low', 'Zones_sup_dem', 'Zone_Status']].to_string())
    
    print("\nTest completed successfully!")

if __name__ == '__main__':
    test_zone_functions()
