from dhanhq import dhanhq
import pandas as pd
import datetime


client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
dhan = dhanhq(client_code, token_id)


SYMBOL = 'NIFTY'
EXCHANGE = 'INDEX'

IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))  # UTC+5:30
MARKET_OPEN_TIME = datetime.time(9, 15)
MARKET_CLOSE_TIME = datetime.time(15, 29)
res = dhan.historical_daily_data(
    security_id='13',
    exchange_segment='IDX_I',
    instrument_type='INDEX',
    expiry_code=0,
    from_date='2022-06-12',
    to_date='2025-06-14'
)


# Convert the 'data' key to a DataFrame
df = pd.DataFrame(res['data'])

# Convert 'timestamp' from epoch seconds to UTC datetime
df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)

# Convert UTC to IST (UTC+5:30)
df['timestamp'] = df['timestamp'].dt.tz_convert('Asia/Kolkata').dt.tz_localize(None)

# print(df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].head(10))

print(df.head(10))
print(df.tail(10))
print("Number of candles fetched:", df.shape[0])